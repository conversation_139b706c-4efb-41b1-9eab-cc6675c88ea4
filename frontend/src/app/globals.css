@import '../styles/design-tokens.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-black text-white;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight text-white;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }
}

@layer components {
  .btn-gradient {
    background: linear-gradient(90deg, #FF9000 0%, #e67e00 100%);
    @apply hover:shadow-xl text-white font-medium transition-all duration-300 shadow-lg;
  }

  .btn-gradient:hover {
    background: linear-gradient(90deg, #e67e00 0%, #cc6600 100%);
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02] bg-white border border-gray-200;
  }

  /* BiHub Admin Form Dark Theme */
  .bihub-admin-form .card {
    @apply bg-gray-800/50 border-gray-700/50 rounded-xl shadow-lg;
  }

  .bihub-admin-form .card-header {
    @apply pb-4;
  }

  .bihub-admin-form .card-title {
    @apply text-lg font-semibold text-white;
  }

  .bihub-admin-form .card-content {
    @apply space-y-4;
  }

  .bihub-admin-form label {
    @apply text-sm font-medium text-white;
  }

  .bihub-admin-form input,
  .bihub-admin-form textarea,
  .bihub-admin-form select {
    @apply bg-gray-700/50 border-gray-600/50 text-white placeholder:text-gray-400;
    @apply focus:border-[#FF9000] focus:ring-[#FF9000]/20;
    @apply rounded-lg transition-colors duration-200;
  }

  .bihub-admin-form input:focus,
  .bihub-admin-form textarea:focus,
  .bihub-admin-form select:focus {
    @apply ring-2 ring-[#FF9000]/20 border-[#FF9000];
  }

  .bihub-admin-form .text-gray-500 {
    @apply text-gray-400;
  }

  .bihub-admin-form .text-gray-600 {
    @apply text-gray-300;
  }

  .bihub-admin-form .border-t {
    @apply border-gray-700/50;
  }

  /* New animations for enhanced ProductCard */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes bounce-soft {
    0%, 100% {
      transform: translateY(0) scale(1);
    }
    50% {
      transform: translateY(-4px) scale(1.05);
    }
  }

  @keyframes twinkle {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-bounce-soft {
    animation: bounce-soft 2s ease-in-out infinite;
  }

  .animate-twinkle {
    animation: twinkle 1.5s ease-in-out infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent;
  }

  .hero-gradient {
    @apply bg-gradient-to-br from-black via-gray-900 to-black;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 3s infinite;
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out forwards;
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(50px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

:root {
  /* Light theme */
  --background: #fefefe;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #10b981;
  --radius: 0.75rem;

  /* Primary brand colors - Indigo & Violet theme */
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --primary-50: #eef2ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;
  --primary-950: #1e1b4b;

  /* Secondary colors - Slate */
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Accent colors - Rose */
  --accent: #fdf2f8;
  --accent-foreground: #be185d;

  /* Destructive */
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;

  /* Success */
  --success: #10b981;
  --success-foreground: #ffffff;

  /* Warning */
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
}

.dark {
  /* Dark theme */
  --background: #0f172a;
  --foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --border: #334155;
  --input: #334155;
  --ring: #818cf8;

  --primary: #818cf8;
  --primary-foreground: #ffffff;

  --secondary: #334155;
  --secondary-foreground: #f8fafc;

  --accent: #334155;
  --accent-foreground: #f8fafc;

  --destructive: #ef4444;
  --destructive-foreground: #f8fafc;

  --success: #22c55e;
  --success-foreground: #f8fafc;

  --warning: #f59e0b;
  --warning-foreground: #f8fafc;
}

* {
  border-color: var(--border);
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom animations */
@keyframes fade-in {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-up {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scale-in {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.3); }
  50% { box-shadow: 0 0 40px rgba(99, 102, 241, 0.6); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.8s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-slow {
  animation: bounce 2s infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .text-responsive-xs { font-size: 0.75rem; }
  .text-responsive-sm { font-size: 0.875rem; }
  .text-responsive-base { font-size: 1rem; }
  .text-responsive-lg { font-size: 1.125rem; }
  .text-responsive-xl { font-size: 1.25rem; }
  .text-responsive-2xl { font-size: 1.5rem; }
  .text-responsive-3xl { font-size: 1.875rem; }
  .text-responsive-4xl { font-size: 2.25rem; }
}

@media (min-width: 641px) and (max-width: 768px) {
  .text-responsive-xs { font-size: 0.875rem; }
  .text-responsive-sm { font-size: 1rem; }
  .text-responsive-base { font-size: 1.125rem; }
  .text-responsive-lg { font-size: 1.25rem; }
  .text-responsive-xl { font-size: 1.5rem; }
  .text-responsive-2xl { font-size: 1.875rem; }
  .text-responsive-3xl { font-size: 2.25rem; }
  .text-responsive-4xl { font-size: 3rem; }
}

@media (min-width: 769px) {
  .text-responsive-xs { font-size: 0.75rem; }
  .text-responsive-sm { font-size: 0.875rem; }
  .text-responsive-base { font-size: 1rem; }
  .text-responsive-lg { font-size: 1.125rem; }
  .text-responsive-xl { font-size: 1.25rem; }
  .text-responsive-2xl { font-size: 1.5rem; }
  .text-responsive-3xl { font-size: 1.875rem; }
  .text-responsive-4xl { font-size: 2.25rem; }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }

  .hover\:shadow-xl:hover {
    box-shadow: none;
  }

  /* Increase touch targets */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-border {
    border-color: currentColor;
  }

  .text-muted-foreground {
    color: currentColor;
    opacity: 0.8;
  }
}

/* Enhanced Cart Animations */
@keyframes bounceIn {
  0% { opacity: 0; transform: scale(0.3) rotate(-10deg); }
  50% { opacity: 1; transform: scale(1.05) rotate(5deg); }
  70% { transform: scale(0.9) rotate(-2deg); }
  100% { opacity: 1; transform: scale(1) rotate(0deg); }
}

@keyframes slideInFromRight {
  0% { opacity: 0; transform: translateX(100px); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes pulseGlow {
  0%, 100% { box-shadow: 0 0 5px rgba(249, 115, 22, 0.4); }
  50% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.8), 0 0 30px rgba(249, 115, 22, 0.4); }
}



.animate-bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Loading Skeleton Animations */
@keyframes skeleton-loading {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* Responsive Improvements */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-4xl {
    font-size: 2rem;
    line-height: 2.5rem;
  }

  .text-5xl {
    font-size: 2.5rem;
    line-height: 3rem;
  }
}

/* Enhanced Mobile Navigation */
@media (max-width: 768px) {
  .mobile-menu-enter {
    transform: translateX(-100%);
    opacity: 0;
  }

  .mobile-menu-enter-active {
    transform: translateX(0);
    opacity: 1;
    transition: transform 300ms ease-out, opacity 300ms ease-out;
  }

  .mobile-menu-exit {
    transform: translateX(0);
    opacity: 1;
  }

  .mobile-menu-exit-active {
    transform: translateX(-100%);
    opacity: 0;
    transition: transform 300ms ease-in, opacity 300ms ease-in;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in,
  .animate-pulse-slow,
  .animate-bounce-slow,
  .animate-shimmer,
  .animate-glow,
  .animate-bounce-in,
  .animate-slide-in-right,
  .animate-pulse-glow {
    animation: none;
  }

  .transition-all,
  .transition-colors,
  .transition-transform {
    transition: none;
  }
}
