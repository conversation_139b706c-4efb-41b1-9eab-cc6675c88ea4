{"id": "ecom-golang-environment", "name": "Bihub API Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "description": "Base URL for the API server", "enabled": true}, {"key": "jwt_token", "value": "", "description": "JWT authentication token (automatically set after login)", "enabled": true}, {"key": "refresh_token", "value": "", "description": "JWT refresh token (automatically set after login)", "enabled": true}, {"key": "user_id", "value": "", "description": "Current authenticated user ID", "enabled": true}, {"key": "session_id", "value": "", "description": "Session ID for guest operations", "enabled": true}, {"key": "first_product_id", "value": "", "description": "ID of the first product (for testing)", "enabled": true}, {"key": "created_product_id", "value": "", "description": "ID of newly created product", "enabled": true}, {"key": "order_id", "value": "", "description": "ID of created order", "enabled": true}, {"key": "order_number", "value": "", "description": "Order number for tracking", "enabled": true}, {"key": "category_id", "value": "", "description": "ID of category", "enabled": true}, {"key": "created_category_id", "value": "", "description": "ID of newly created category", "enabled": true}, {"key": "cart_id", "value": "", "description": "ID of user's cart", "enabled": true}, {"key": "payment_id", "value": "", "description": "ID of payment transaction", "enabled": true}, {"key": "checkout_session_id", "value": "", "description": "Stripe checkout session ID", "enabled": true}, {"key": "checkout_session_url", "value": "", "description": "Stripe checkout session URL", "enabled": true}, {"key": "uploaded_file_id", "value": "", "description": "ID of uploaded file", "enabled": true}, {"key": "uploaded_file_url", "value": "", "description": "URL of uploaded file", "enabled": true}, {"key": "admin_file_id", "value": "", "description": "ID of admin uploaded file", "enabled": true}, {"key": "public_file_id", "value": "", "description": "ID of public uploaded file", "enabled": true}, {"key": "uploaded_document_id", "value": "", "description": "ID of uploaded document", "enabled": true}, {"key": "refund_id", "value": "", "description": "ID of refund transaction", "enabled": true}, {"key": "admin_user_id", "value": "", "description": "ID of admin user for testing", "enabled": true}, {"key": "review_id", "value": "", "description": "ID of product review", "enabled": true}, {"key": "wishlist_id", "value": "", "description": "ID of wishlist item", "enabled": true}, {"key": "coupon_id", "value": "", "description": "ID of coupon/promotion", "enabled": true}, {"key": "notification_id", "value": "", "description": "ID of notification", "enabled": true}, {"key": "address_id", "value": "", "description": "ID of user address", "enabled": true}, {"key": "shipping_id", "value": "", "description": "ID of shipping record", "enabled": true}, {"key": "inventory_id", "value": "", "description": "ID of inventory record", "enabled": true}, {"key": "brand_id", "value": "", "description": "ID of product brand", "enabled": true}, {"key": "color_attribute_id", "value": "", "description": "ID of color attribute", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "description": "Test email for registration/login", "enabled": true}, {"key": "test_password", "value": "TestPassword123!", "description": "Test password for registration/login", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "description": "Admin email for testing", "enabled": true}, {"key": "admin_password", "value": "AdminPassword123!", "description": "Admin password for testing", "enabled": true}, {"key": "oauth_state", "value": "", "description": "OAuth state parameter for Google authentication", "enabled": true}, {"key": "facebook_oauth_state", "value": "", "description": "OAuth state parameter for Facebook authentication", "enabled": true}, {"key": "first_session_id", "value": "", "description": "ID of first session for testing session invalidation", "enabled": true}, {"key": "filter_set_id", "value": "", "description": "ID of created filter set for testing", "enabled": true}, {"key": "verification_token", "value": "", "description": "Email verification token", "enabled": true}, {"key": "reset_token", "value": "", "description": "Password reset token", "enabled": true}, {"key": "laptop_category_id", "value": "", "description": "ID of laptop category for testing", "enabled": true}, {"key": "gaming_brand_id", "value": "", "description": "ID of gaming brand for testing", "enabled": true}, {"key": "comparison_id", "value": "", "description": "ID of product comparison for testing", "enabled": true}, {"key": "moderator_product_id", "value": "", "description": "ID of product created by moderator", "enabled": true}, {"key": "moderator_file_id", "value": "", "description": "ID of file uploaded by moderator", "enabled": true}, {"key": "moderator_file_url", "value": "", "description": "URL of file uploaded by moderator", "enabled": true}, {"key": "shipping_method_id", "value": "", "description": "ID of shipping method for testing", "enabled": true}, {"key": "valid_coupon_code", "value": "", "description": "Valid coupon code for testing", "enabled": true}, {"key": "discount_amount", "value": "", "description": "Discount amount from coupon validation", "enabled": true}, {"key": "reservation_id", "value": "", "description": "ID of inventory reservation", "enabled": true}, {"key": "analytics_event_id", "value": "", "description": "ID of tracked analytics event", "enabled": true}, {"key": "recommendation_id", "value": "", "description": "ID of recommendation set", "enabled": true}, {"key": "search_query", "value": "laptop", "description": "Default search query for testing", "enabled": true}, {"key": "tracking_number", "value": "1Z999AA1234567890", "description": "Sample tracking number for testing", "enabled": true}, {"key": "category_slug", "value": "electronics", "description": "Sample category slug for testing", "enabled": true}, {"key": "checkout_session_id", "value": "", "description": "Checkout session ID for order creation", "enabled": true}, {"key": "checkout_session_token", "value": "", "description": "Checkout session token", "enabled": true}, {"key": "ram_attribute_id", "value": "", "description": "RAM attribute ID for product attributes", "enabled": true}, {"key": "storage_attribute_id", "value": "", "description": "Storage attribute ID for product attributes", "enabled": true}, {"key": "material_attribute_id", "value": "", "description": "Material attribute ID for product attributes", "enabled": true}, {"key": "coupon_id", "value": "", "description": "ID of created coupon", "enabled": true}, {"key": "file_id", "value": "", "description": "ID of uploaded file", "enabled": true}, {"key": "inventory_id", "value": "", "description": "ID of inventory item", "enabled": true}, {"key": "warehouse_id", "value": "", "description": "ID of warehouse", "enabled": true}, {"key": "payment_method_id", "value": "", "description": "ID of payment method", "enabled": true}, {"key": "shipping_method_id", "value": "", "description": "ID of shipping method", "enabled": true}, {"key": "purchase_order_id", "value": "", "description": "ID of purchase order", "enabled": true}, {"key": "return_id", "value": "", "description": "ID of return request", "enabled": true}, {"key": "report_id", "value": "", "description": "ID of generated report", "enabled": true}, {"key": "backup_id", "value": "", "description": "ID of system backup", "enabled": true}, {"key": "session_id", "value": "", "description": "ID of user session", "enabled": true}, {"key": "moderator_product_id", "value": "", "description": "ID of product created by moderator", "enabled": true}, {"key": "moderator_file_id", "value": "", "description": "ID of file uploaded by moderator", "enabled": true}, {"key": "moderator_file_url", "value": "", "description": "URL of file uploaded by moderator", "enabled": true}, {"key": "second_user_id", "value": "", "description": "ID of second user for bulk operations", "enabled": true}, {"key": "inactive_user_id", "value": "", "description": "ID of inactive user for testing", "enabled": true}, {"key": "admin_token", "value": "", "description": "JWT token for admin authentication", "enabled": true}, {"key": "session_id", "value": "", "description": "Session ID for guest operations", "enabled": true}, {"key": "warehouse_id", "value": "", "description": "ID of warehouse for inventory operations", "enabled": true}, {"key": "inventory_id", "value": "", "description": "ID of inventory record", "enabled": true}, {"key": "comparison_id", "value": "", "description": "ID of product comparison", "enabled": true}, {"key": "notification_id", "value": "", "description": "ID of notification", "enabled": true}, {"key": "websocket_url", "value": "ws://localhost:8080/ws", "description": "WebSocket connection URL", "enabled": true}, {"key": "search_index", "value": "products", "description": "Search index name", "enabled": true}, {"key": "filter_set_id", "value": "", "description": "ID of product filter set", "enabled": true}, {"key": "address_id", "value": "", "description": "ID of user address", "enabled": true}, {"key": "payment_method_id", "value": "", "description": "ID of payment method", "enabled": true}, {"key": "shipment_id", "value": "", "description": "ID of shipment", "enabled": true}, {"key": "return_id", "value": "", "description": "ID of return request", "enabled": true}, {"key": "backup_id", "value": "", "description": "ID of system backup", "enabled": true}, {"key": "moderator_product_id", "value": "", "description": "ID of product created by moderator", "enabled": true}, {"key": "purchase_order_id", "value": "", "description": "ID of purchase order", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}